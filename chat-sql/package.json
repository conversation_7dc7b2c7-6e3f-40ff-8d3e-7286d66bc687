{"name": "chat-sql", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "generate-git": "node scripts/generate-git-info.js", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@monaco-editor/react": "^4.7.0", "@mui/icons-material": "^7.0.2", "@mui/lab": "^7.0.0-beta.11", "@mui/material": "^7.0.2", "@mui/material-nextjs": "^7.2.0", "@mui/x-data-grid": "^7.28.3", "@tsparticles/slim": "^3.8.1", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@xyflow/react": "^12.5.5", "antd": "^5.24.6", "axios": "^1.11.0", "d3-dag": "^1.1.0", "idb": "^8.0.3", "katex": "^0.16.22", "monaco-editor": "^0.52.2", "next": "15.3.0", "next-themes": "^0.4.6", "node-sql-parser": "^5.3.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-katex": "^3.1.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "simple-git": "^3.27.0", "tsparticles-engine": "^2.12.0", "uuid": "^11.1.0"}, "resolutions": {"antd": {"react": "$react", "react-dom": "$react-dom"}}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/katex": "^0.16.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "monaco-editor-webpack-plugin": "^7.1.0", "tailwindcss": "^4", "typescript": "^5"}}